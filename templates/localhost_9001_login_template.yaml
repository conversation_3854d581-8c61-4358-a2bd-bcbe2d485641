# Playwright Automation Flow for MinIO Console Login
name: MinIO Console Login Template
description: A reusable flow for attempting to log into a MinIO Console instance at localhost:9001.

# Define the variables that the brute-force executor will use.
variables:
  - username
  - password

# Define the failure condition for the brute-force attack.
# The loop will continue if these conditions are met (indicating login failure).
failure_condition:
  any:
    # Failure condition 1: HTTP 401 Unauthorized error in console
    - type: console_error
      target:
        type: text
        value: "401 (Unauthorized)"
    
    # Failure condition 2: Specific error message appears on page
    - type: element_is_visible
      target:
        type: text
        value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
    
    # Failure condition 3: Still on login page after attempt (URL hasn't changed)
    - type: url_contains
      target:
        type: text
        value: "/login"

steps:
  - name: "Navigate to Login Page"
    action: "navigate"
    parameters:
      url: "http://localhost:9001/login"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to the MinIO Console login page."

  - name: "Wait for Page Load"
    action: "wait"
    parameters:
      timeout: 3000
    description: "Wait for the login form to fully load."

  - name: "Fill in Username"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Username"
    parameters:
      text: "{username}"
    description: "Locates the username input field and types the value from the dictionary."

  - name: "Fill in Password"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Password"
    parameters:
      text: "{password}"
    description: "Locates the password input field and types the value from the dictionary."

  - name: "Click Login Button"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Login"
    description: "Locates and clicks the login button to submit the credentials."

  - name: "Wait for Response"
    action: "wait"
    parameters:
      timeout: 5000
    description: "Wait for the server response and page update after login attempt."

  - name: "Check for Error Message"
    action: "wait_for_element"
    target:
      type: "text"
      value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
    parameters:
      timeout: 2000
      state: "visible"
    description: "Check if the error message appears indicating login failure."
    optional: true

# Additional configuration for the executor
config:
  # Browser settings
  browser:
    headless: false
    timeout: 30000
    
  # Retry settings
  retry:
    max_attempts: 3
    delay_between_attempts: 1000
    
  # Success detection
  success_indicators:
    - url_change: true  # URL should change from /login if successful
    - url_not_contains: "/login"  # Should not contain /login after success
    
  # Error handling
  error_handling:
    ignore_console_errors: false
    screenshot_on_failure: true
    continue_on_timeout: true

# Notes for executor implementation:
# 1. The MinIO Console requires both username and password fields to be filled before the login button becomes enabled
# 2. Failed login attempts return HTTP 401 and display an error message
# 3. Successful login would redirect away from the /login page
# 4. The error message may auto-dismiss after a few seconds
# 5. Console errors should be monitored for 401 Unauthorized responses
