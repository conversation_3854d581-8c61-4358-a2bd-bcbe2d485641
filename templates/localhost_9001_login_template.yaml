# Playwright Automation Flow for MinIO Console Login
name: MinIO Console Login Template
description: A reusable flow for attempting to log into a MinIO Console instance at localhost:9001.

# Define the variables that the brute-force executor will use.
variables:
  - username
  - password

# Define the failure condition for the brute-force attack.
# The loop will stop if these conditions are met.
failure_condition:
  any:
    # Failure condition: Error message containing ErrorResponse
    - type: element_is_visible
      target:
        type: text
        value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"

steps:
  - name: "Navigate to Login Page"
    action: "navigate"
    parameters:
      url: "http://localhost:9001/login"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to the MinIO Console login page."

  - name: "Fill in Username"
    action: "type"
    target:
      type: "selector"
      value: "#accessKey"
    parameters:
      text: "{username}"
    description: "Locates the username input field by ID and types the value from the dictionary."

  - name: "Fill in Password"
    action: "type"
    target:
      type: "selector"
      value: "#secretKey"
    parameters:
      text: "{password}"
    description: "Locates the password input field by ID and types the value from the dictionary."

  - name: "Click Login Button"
    action: "click"
    target:
      type: "selector"
      value: "#do-login"
    description: "Locates and clicks the login button to submit the credentials."