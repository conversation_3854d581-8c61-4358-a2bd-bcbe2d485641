# Playwright Automation Flow for Connectivity Test
name: Connectivity Test Template
description: A test flow for checking connectivity functionality.

# Define the variables that the brute-force executor will use.
variables:
  - username
  - password

# Define the success condition for the brute-force attack.
# The loop will stop if these conditions are met.
failure_condition:
  any:
    # Failure condition: The specific error message alert becomes visible.
    - type: element_is_visible
      target:
        type: text
        value: "Login failed"

steps:
  - name: "Navigate to Test Page"
    action: "navigate"
    parameters:
      url: "https://httpbin.org/status/200"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to a test page that should be accessible."

  - name: "Fill in Username"
    action: "type"
    target:
      type: "selector"
      value: "input[name=\"username\"]"
    parameters:
      text: "{username}"
    description: "Locates the username input field and types the value from the dictionary."

  - name: "Fill in Password"
    action: "type"
    target:
      type: "selector"
      value: "input[name=\"password\"]"
    parameters:
      text: "{password}"
    description: "Locates the password input field and types the value from the dictionary."

  - name: "Click Login Button"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Login"
    description: "Locates and clicks the login button to submit the credentials."
