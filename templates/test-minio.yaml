name: <PERSON><PERSON> Login Flow
description: Automates login to MinIO Console at http://localhost:9001/login
steps:
  - name: Navigate to Login Page
    description: Go to the MinIO login URL
    action: navigate
    parameters:
      url: http://localhost:9001/login
      waitUntil: load
  - name: Enter Username
    description: Type username into the username field
    action: type
    target:
      type: role
      value: textbox
      options:
        name: Username
    parameters:
      text: "{username}"
  - name: Enter Password
    description: Type password into the password field
    action: type
    target:
      type: role
      value: textbox
      options:
        name: Password
    parameters:
      text: "{password}"
  - name: Click Login Button
    description: Click the login button
    action: click
    target:
      type: role
      value: button
      options:
        name: Login

failure_condition:
  any:
    - type: element_is_visible
      target:
        type: text
        value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>" # Updated: Based on failed login to MinIO Console
