# Playwright Automation Flow for Grafana Login
name: Grafana Login Template
description: A reusable flow for attempting to log into a Grafana instance at localhost:3000.

# Define the variables that the brute-force executor will use.
variables:
  - username
  - password

# Define the success condition for the brute-force attack.
# The loop will stop if these conditions are met.
failure_condition:
  any:
    # Failure condition: The specific error message alert becomes visible.
    - type: element_is_visible
      target:
        type: text
        value: "Invalid username or password"

steps:
  - name: "Navigate to Login Page"
    action: "navigate"
    parameters:
      url: "http://localhost:3000/login"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to the Grafana login page."

  - name: "Fill in Username"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Username input field"
    parameters:
      text: "{username}"
    description: "Locates the username input field and types the value from the dictionary."

  - name: "Fill in Password"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Password input field"
    parameters:
      text: "{password}"
    description: "Locates the password input field and types the value from the dictionary."

  - name: "Click Login Button"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Login button"
    description: "Locates and clicks the login button to submit the credentials."